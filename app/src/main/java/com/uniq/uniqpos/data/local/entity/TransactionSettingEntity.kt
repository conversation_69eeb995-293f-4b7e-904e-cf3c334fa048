package com.uniq.uniqpos.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.annotations.SerializedName

/**
 * Entity for transaction settings
 */
@Entity(tableName = "transaction_setting")
data class TransactionSettingEntity(
    @PrimaryKey
    @SerializedName("id")
    val id: Int,

    @SerializedName("outlet_id")
    val outletId: Int,

    @SerializedName("label")
    val label: String,

    @SerializedName("type")
    val type: String,

    @SerializedName("option_values")
    val optionValues: String,

    @SerializedName("is_required")
    val isRequired: Boolean,

    @SerializedName("display_order")
    val displayOrder: Int,

    @SerializedName("created_at")
    val createdAt: Long,

    @SerializedName("updated_at")
    val updatedAt: Long,

    var synced: Boolean = true
)
