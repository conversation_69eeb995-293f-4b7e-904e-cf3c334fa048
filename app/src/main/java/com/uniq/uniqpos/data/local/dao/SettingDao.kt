package com.uniq.uniqpos.data.local.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.uniq.uniqpos.data.local.entity.KitchenDisplayEntity
import com.uniq.uniqpos.data.local.entity.PrinterClosingShiftEntity
import com.uniq.uniqpos.data.local.entity.PrinterEntity
import com.uniq.uniqpos.data.local.entity.PrinterTicketEntity
import com.uniq.uniqpos.data.local.entity.TransactionSettingEntity

/**
 * Created by ANNASBlackHat on 18/10/2017.
 */

@Dao
interface SettingDao {

    @Query("SELECT * FROM printer")
    fun getPrintersLive(): LiveData<List<PrinterEntity>>

    @Query("SELECT * FROM printer")
    fun getPrinters(): List<PrinterEntity>

    @Query("SELECT * FROM printer WHERE address = :id LIMIT 1")
    fun getPrinterByAddress(id: String): PrinterEntity

    @Query("SELECT * FROM printer WHERE synced = 0")
    fun getUnSyncedPrinter(): List<PrinterEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addPrinter(printerEntity: PrinterEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addPrinters(printerEntity: List<PrinterEntity>)

    @Update
    fun updatePrinter(printerEntity: PrinterEntity)

    @Delete
    fun deletePrinter(printerEntity: PrinterEntity)

    @Query("DELETE FROM printer WHERE printerSettingId= :id")
    fun deletePrinterById(id: Int)

    @Query("SELECT * FROM printer_closingshift")
    fun getPrinterClosingShift(): List<PrinterClosingShiftEntity>

    @Query("SELECT * FROM printer_closingshift WHERE printerSettingFkid = :id")
    fun getPrinterCloseShiftByPrinterId(id: Int): List<PrinterClosingShiftEntity>

    @Query("DELETE FROM printer_closingshift WHERE closingshiftId = :id")
    fun deletePrinterClosingShiftById(id: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addPrintersClosingShift(printerClosingShiftEntity: List<PrinterClosingShiftEntity>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addPrinterTickets(list: List<PrinterTicketEntity>)

    @Query("SELECT * FROM printer_ticket WHERE settingType='printorder'")
    fun getPrinterTicketOrders(): List<PrinterTicketEntity>

    @Query("SELECT * FROM printer_ticket WHERE settingType='printorder' and printerSettingFkid = :id")
    fun getPrinterTicketOrderById(id: Int): List<PrinterTicketEntity>

    @Delete
    fun deletePrinterTicket(printerTicketEntity: PrinterTicketEntity)

//    --------- KITCHEN DISPLAY ---------

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addKitchenDisplay(kitchenDisplayEntity: KitchenDisplayEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addKitchenDisplays(kitchenDisplayEntity: List<KitchenDisplayEntity>)

    @Update
    fun updateKitchenDisplay(kitchenDisplayEntity: KitchenDisplayEntity)

    @Query("SELECT * FROM kitchen_display")
    fun getKitchenDisplays(): LiveData<List<KitchenDisplayEntity>>

    @Query("DELETE FROM kitchen_display WHERE setting_kitchen_display_id = :id")
    fun removeKitchenDisplay(id: Int)

//    --------- TRANSACTION SETTINGS ---------

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addTransactionSettings(transactionSettings: List<TransactionSettingEntity>)
    @Query("SELECT * FROM transaction_setting ORDER BY displayOrder ASC")
    fun getTransactionSettings(): LiveData<List<TransactionSettingEntity>>

    @Query("SELECT * FROM transaction_setting ORDER BY displayOrder ASC")
    fun getTransactionSettingsSync(): List<TransactionSettingEntity>

}